"""
FastAPI Routes for Voice Over Agent

This module provides REST API endpoints for the Voice Over Agent
for converting video voice-overs using Azure AI Speech Services.
"""

import time
import uuid
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, HttpUrl
from typing import Optional, Dict, Any
from Voice_Over_Agent.utils.voice_processor import process_voice_over_request
from Voice_Over_Agent.state.state import VoiceOverRequest, VoiceOverResponse
from config import Config

router = APIRouter()

class VoiceOverProcessRequest(BaseModel):
    """Request model for voice over processing"""
    video_url: HttpUrl
    voice_name: Optional[str] = "aria"

class VoiceOverProcessResponse(BaseModel):
    """Response model for voice over processing"""
    success: bool
    message: str
    request_id: Optional[str] = None
    output_url: Optional[str] = None
    transcript: Optional[str] = None
    transcript_length: Optional[int] = None
    processing_time: Optional[float] = None
    error: Optional[str] = None

class VoiceOverStatusResponse(BaseModel):
    """Response model for voice over status"""
    request_id: str
    status: str
    message: str
    progress: Optional[int] = None
    result: Optional[Dict[str, Any]] = None

# In-memory storage for request status (in production, use Redis or database)
request_status: Dict[str, Dict[str, Any]] = {}

@router.get("/voice-over/health")
async def health_check():
    """Health check endpoint for Voice Over Agent"""
    try:
        # Basic configuration check
        config_status = {
            "azure_speech_configured": bool(Config.AZURE_SPEECH_KEY),
            "azure_storage_configured": bool(Config.AZURE_STORAGE_CONNECTION_STRING),
            "voice_options_available": bool(Config.AZURE_VOICE_OPTIONS)
        }
        
        return {
            "status": "healthy",
            "service": "Voice Over Agent",
            "version": "1.0.0",
            "configuration": config_status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/voice-over/voices")
async def get_available_voices():
    """Get list of available Azure neural voices"""
    try:
        return {
            "voices": Config.AZURE_VOICE_OPTIONS,
            "default_voice": "aria"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")

@router.post("/voice-over/process", response_model=VoiceOverProcessResponse)
async def process_voice_over(request: VoiceOverProcessRequest):
    """
    Process video for voice over conversion
    
    This endpoint:
    1. Downloads the video from the provided URL
    2. Extracts audio from the video
    3. Transcribes the audio using Azure Speech Services
    4. Generates new AI voice using Azure Speech Services
    5. Replaces the original audio with AI-generated voice
    6. Returns the processed video URL
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        # Validate voice name
        if request.voice_name not in Config.AZURE_VOICE_OPTIONS:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid voice name. Available voices: {list(Config.AZURE_VOICE_OPTIONS.keys())}"
            )
        
        # Create Voice Over Request
        voice_request = VoiceOverRequest(
            video_url=str(request.video_url),
            voice_name=request.voice_name,
            request_id=request_id
        )
        
        # Update request status
        request_status[request_id] = {
            "status": "processing",
            "message": "Starting voice over conversion...",
            "progress": 0,
            "start_time": start_time
        }
        
        # Process the voice over request
        result: VoiceOverResponse = process_voice_over_request(
            video_url=str(request.video_url),
            voice_name=request.voice_name
        )
        
        processing_time = time.time() - start_time
        
        # Update request status
        request_status[request_id] = {
            "status": "completed" if result.success else "failed",
            "message": result.message,
            "progress": 100,
            "result": result.dict(),
            "processing_time": processing_time
        }
        
        if result.success:
            return VoiceOverProcessResponse(
                success=True,
                message="Voice over conversion completed successfully",
                request_id=request_id,
                output_url=result.output_url,
                transcript=result.transcript,
                transcript_length=len(result.transcript) if result.transcript else None,
                processing_time=processing_time
            )
        else:
            return VoiceOverProcessResponse(
                success=False,
                message="Voice over conversion failed",
                request_id=request_id,
                error=result.error,
                processing_time=processing_time
            )
            
    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        error_message = f"Unexpected error during voice over processing: {str(e)}"
        
        # Update request status
        request_status[request_id] = {
            "status": "failed",
            "message": error_message,
            "progress": 0,
            "error": str(e),
            "processing_time": processing_time
        }
        
        return VoiceOverProcessResponse(
            success=False,
            message="Voice over conversion failed",
            request_id=request_id,
            error=error_message,
            processing_time=processing_time
        )

@router.post("/voice-over/process-async")
async def process_voice_over_async(request: VoiceOverProcessRequest, background_tasks: BackgroundTasks):
    """
    Process video for voice over conversion asynchronously
    
    This endpoint starts the processing in the background and returns immediately
    with a request ID that can be used to check the status.
    """
    request_id = str(uuid.uuid4())
    
    try:
        # Validate voice name
        if request.voice_name not in Config.AZURE_VOICE_OPTIONS:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid voice name. Available voices: {list(Config.AZURE_VOICE_OPTIONS.keys())}"
            )
        
        # Initialize request status
        request_status[request_id] = {
            "status": "queued",
            "message": "Voice over conversion queued for processing",
            "progress": 0,
            "start_time": time.time()
        }
        
        # Add background task
        background_tasks.add_task(
            process_voice_over_background,
            request_id,
            str(request.video_url),
            request.voice_name
        )
        
        return {
            "success": True,
            "message": "Voice over conversion started",
            "request_id": request_id,
            "status_url": f"/voice-over/status/{request_id}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start voice over processing: {str(e)}")

@router.get("/voice-over/status/{request_id}", response_model=VoiceOverStatusResponse)
async def get_voice_over_status(request_id: str):
    """Get the status of a voice over processing request"""
    
    if request_id not in request_status:
        raise HTTPException(status_code=404, detail="Request ID not found")
    
    status_info = request_status[request_id]
    
    return VoiceOverStatusResponse(
        request_id=request_id,
        status=status_info["status"],
        message=status_info["message"],
        progress=status_info.get("progress"),
        result=status_info.get("result")
    )

async def process_voice_over_background(request_id: str, video_url: str, voice_name: str):
    """Background task for processing voice over conversion"""
    
    try:
        # Update status to processing
        request_status[request_id].update({
            "status": "processing",
            "message": "Processing voice over conversion...",
            "progress": 10
        })
        
        # Process the voice over request
        result: VoiceOverResponse = process_voice_over_request(
            video_url=video_url,
            voice_name=voice_name
        )
        
        processing_time = time.time() - request_status[request_id]["start_time"]
        
        # Update final status
        request_status[request_id].update({
            "status": "completed" if result.success else "failed",
            "message": result.message,
            "progress": 100,
            "result": result.dict(),
            "processing_time": processing_time
        })
        
    except Exception as e:
        processing_time = time.time() - request_status[request_id]["start_time"]
        request_status[request_id].update({
            "status": "failed",
            "message": f"Processing failed: {str(e)}",
            "progress": 0,
            "error": str(e),
            "processing_time": processing_time
        })
