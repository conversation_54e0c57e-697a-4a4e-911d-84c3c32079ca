"""
Voice Over Agent Specific Settings

This file contains Azure-specific configurations for the Voice Over Agent.
General configurations should be in the main config.py file.
"""

import os

# Azure Speech Services Configuration
AZURE_SPEECH_KEY = "7vEN1vU4kDITosB5kdtfro11U330ybXV2dZYzJZaCj66B7Y09Q2fJQQJ99BGACYeBjFXJ3w3AAAYACOGbGQD"
AZURE_SPEECH_REGION = "eastus"  # e.g., eastus, westus2, etc.

# Azure Storage Configuration
AZURE_STORAGE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=qmigstg1137;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"

# Azure Blob Storage Containers
AZURE_CONTAINER_VIDEOS = "qmvideos"
AZURE_CONTAINER_AUDIO = "qmaudio"
AZURE_CONTAINER_OUTPUT = "qmoutput"

# Azure Speech Services Settings
AZURE_SPEECH_STT_CONFIG = {
    "language": "en-US",
    "format": "detailed",
    "profanity_filter": "masked"
}

AZURE_SPEECH_TTS_CONFIG = {
    "voice_name": "en-US-AriaNeural",  # High-quality neural voice
    "language": "en-US",
    "output_format": "audio-24khz-48kbitrate-mono-mp3"
}

# Alternative voice options
AZURE_VOICE_OPTIONS = {
    "aria": "en-US-AriaNeural",
    "jenny": "en-US-JennyNeural", 
    "guy": "en-US-GuyNeural",
    "davis": "en-US-DavisNeural",
    "jane": "en-US-JaneNeural"
}

# Output Settings
OUTPUT_FOLDER = "output"
TEMP_FOLDER = "temp"
LOG_FILE = "azure_voice_conversion.log"

# Processing Settings
CHUNK_SIZE = 8192
MAX_WAIT_TIME = 600  # 10 minutes for larger files

# Create necessary directories
def create_directories():
    """Create necessary directories if they don't exist"""
    directories = [OUTPUT_FOLDER, TEMP_FOLDER]
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created directory: {directory}")

if __name__ == "__main__":
    create_directories()
