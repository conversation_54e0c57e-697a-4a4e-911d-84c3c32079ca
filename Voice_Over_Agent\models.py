"""
Pydantic Models for Voice Over Agent

This module contains all the Pydantic models used by the Voice Over Agent
for request/response validation and data structure.
"""

from pydantic import BaseModel, HttpUrl
from typing import Optional

class VoiceOverProcessRequest(BaseModel):
    """Request model for voice over processing"""
    video_url: HttpUrl
    voice_name: Optional[str] = "aria"

class VoiceOverProcessResponse(BaseModel):
    """Response model for voice over processing"""
    success: bool
    message: str
    output_url: Optional[str] = None
    transcript: Optional[str] = None
    processing_time: Optional[float] = None
    error: Optional[str] = None
