"""
Voice Over Agent State Management

This module defines the state structures for voice over conversion requests
and responses using Pydantic models for validation and serialization.
"""

from typing import Optional
from pydantic import BaseModel, Field


class VoiceOverRequest(BaseModel):
    """Request model for voice over conversion"""
    
    video_url: str = Field(..., description="URL of the video to process")
    voice_name: Optional[str] = Field(default="aria", description="Azure neural voice to use")
    
    class Config:
        """Pydantic configuration"""
        json_encoders = {
            # Add any custom encoders if needed
        }


class VoiceOverResponse(BaseModel):
    """Response model for voice over conversion"""
    
    success: bool = Field(..., description="Whether the conversion was successful")
    output_url: Optional[str] = Field(default=None, description="URL of the processed video")
    error_message: Optional[str] = Field(default=None, description="Error message if conversion failed")
    processing_time: Optional[float] = Field(default=None, description="Processing time in seconds")
    
    class Config:
        """Pydantic configuration"""
        json_encoders = {
            # Add any custom encoders if needed
        }
