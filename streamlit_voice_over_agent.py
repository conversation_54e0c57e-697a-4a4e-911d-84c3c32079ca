"""
Streamlit Application for Voice Over Agent

This application provides a user-friendly interface for the Voice Over Agent
for converting video voice-overs using Azure AI Speech Services.
"""

import streamlit as st
import requests
import time
from typing import Optional
from config import Config

# Page configuration
st.set_page_config(
    page_title="🎤 Voice Over Agent",
    page_icon="🎤",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        margin: 1rem 0;
    }
    .error-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        margin: 1rem 0;
    }
    .info-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
        margin: 1rem 0;
    }
    .stButton > button {
        width: 100%;
        background-color: #007bff;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        font-weight: bold;
    }
    .stButton > button:hover {
        background-color: #0056b3;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """Main Streamlit application"""
    
    # Header
    st.markdown('<div class="main-header">🎤 Voice Over Agent</div>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar for configuration
    with st.sidebar:
        st.markdown("### ⚙️ Configuration")
        
        # API Configuration
        api_host = st.text_input(
            "API Host",
            value="http://localhost:8000",
            help="Base URL for the Voice Over Agent API"
        )
        
        # Voice Selection
        st.markdown("### 🎵 Voice Options")
        voice_options = {
            "aria": "en-US-AriaNeural (Female, Natural)",
            "jenny": "en-US-JennyNeural (Female, Friendly)", 
            "guy": "en-US-GuyNeural (Male, Professional)",
            "davis": "en-US-DavisNeural (Male, Warm)",
            "jane": "en-US-JaneNeural (Female, Confident)"
        }
        
        selected_voice = st.selectbox(
            "Select Voice",
            options=list(voice_options.keys()),
            format_func=lambda x: voice_options[x],
            index=0
        )
        
        # Azure Configuration Status
        st.markdown("### 🔧 Azure Services")
        if hasattr(Config, 'AZURE_SPEECH_KEY') and Config.AZURE_SPEECH_KEY:
            st.success("✅ Azure Speech Services Configured")
        else:
            st.error("❌ Azure Speech Services Not Configured")
            
        if hasattr(Config, 'AZURE_STORAGE_CONNECTION_STRING') and Config.AZURE_STORAGE_CONNECTION_STRING:
            st.success("✅ Azure Storage Configured")
        else:
            st.error("❌ Azure Storage Not Configured")
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown('<div class="section-header">📹 Video Input</div>', unsafe_allow_html=True)
        
        # Input method selection
        input_method = st.radio(
            "Choose input method:",
            ["Video URL", "File Upload"],
            horizontal=True
        )
        
        video_input = None
        
        if input_method == "Video URL":
            video_input = st.text_input(
                "Video URL",
                placeholder="https://example.com/video.mp4",
                help="Enter the URL of the video you want to process"
            )
            
            if video_input:
                st.info(f"📹 Video URL: {video_input}")
                
        else:  # File Upload
            uploaded_file = st.file_uploader(
                "Upload Video File",
                type=['mp4', 'avi', 'mov', 'mkv', 'webm'],
                help="Upload a video file to process"
            )
            
            if uploaded_file:
                st.info(f"📁 Uploaded: {uploaded_file.name} ({uploaded_file.size} bytes)")
                # For file upload, we would need to save the file and provide a URL
                # This would require additional backend handling
                st.warning("⚠️ File upload requires backend file handling - use Video URL for now")
        
        # Process button
        if st.button("🚀 Start Voice Over Conversion", type="primary"):
            if not video_input:
                st.error("❌ Please provide a video URL")
            else:
                process_video(api_host, video_input, selected_voice)
    
    with col2:
        st.markdown('<div class="section-header">ℹ️ Information</div>', unsafe_allow_html=True)
        
        st.markdown("""
        <div class="info-box">
        <h4>🎯 What this does:</h4>
        <ul>
            <li>Downloads video from URL</li>
            <li>Extracts audio track</li>
            <li>Transcribes speech using Azure AI</li>
            <li>Generates new AI voice</li>
            <li>Replaces original audio</li>
            <li>Outputs final video</li>
        </ul>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="info-box">
        <h4>📋 Supported formats:</h4>
        <ul>
            <li>Video: MP4, AVI, MOV, MKV, WebM</li>
            <li>Audio: Automatic extraction</li>
            <li>Languages: English (en-US)</li>
        </ul>
        </div>
        """, unsafe_allow_html=True)

def process_video(api_host: str, video_url: str, voice_name: str):
    """Process video through the Voice Over Agent API with background processing"""

    try:
        # Create progress indicators
        progress_bar = st.progress(0)
        status_text = st.empty()

        # Prepare API request
        api_url = f"{api_host}/ai/voice-over/process"
        payload = {
            "video_url": video_url,
            "voice_name": voice_name
        }

        status_text.text("🔄 Starting voice over conversion...")
        progress_bar.progress(10)

        # Make API request to start background processing
        response = requests.post(api_url, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()

            if result.get("success"):
                request_id = result.get("request_id")
                status_url = f"{api_host}/ai/voice-over/status/{request_id}"

                status_text.text("✅ Processing started! Monitoring progress...")
                progress_bar.progress(20)

                # Poll for status updates
                poll_status(status_url, progress_bar, status_text)

            else:
                st.error(f"❌ Failed to start processing: {result.get('message', 'Unknown error')}")

        else:
            progress_bar.progress(0)
            status_text.text("❌ Request failed")
            st.error(f"API Error: {response.status_code} - {response.text}")

    except requests.exceptions.Timeout:
        st.error("⏰ Request timed out. Please try again.")
    except requests.exceptions.RequestException as e:
        st.error(f"🌐 Network error: {str(e)}")
    except Exception as e:
        st.error(f"💥 Unexpected error: {str(e)}")

def poll_status(status_url: str, progress_bar, status_text):
    """Poll the status endpoint until processing is complete"""

    max_polls = 120  # 10 minutes with 5-second intervals
    poll_count = 0

    while poll_count < max_polls:
        try:
            response = requests.get(status_url, timeout=10)

            if response.status_code == 200:
                status_data = response.json()
                status = status_data.get("status", "unknown")
                message = status_data.get("message", "Processing...")
                progress = status_data.get("progress", 0)

                # Update UI
                status_text.text(f"🔄 {message}")
                progress_bar.progress(min(20 + (progress * 0.8), 100))  # Scale progress to 20-100%

                if status == "completed":
                    display_results(status_data.get("result", {}))
                    break
                elif status == "failed":
                    st.error(f"❌ Processing failed: {message}")
                    break

            else:
                st.error(f"❌ Status check failed: {response.status_code}")
                break

        except Exception as e:
            st.error(f"❌ Error checking status: {str(e)}")
            break

        poll_count += 1
        time.sleep(5)  # Wait 5 seconds before next poll

    if poll_count >= max_polls:
        st.warning("⏰ Processing is taking longer than expected. Please check back later.")

def display_results(result_data: dict):
    """Display the final results"""

    st.markdown('<div class="section-header">🎉 Results</div>', unsafe_allow_html=True)

    if result_data.get("success"):
        processing_time = result_data.get("processing_time", "N/A")
        transcript = result_data.get("transcript", "")

        st.markdown(f"""
        <div class="success-box">
        <h4>✅ Voice Over Conversion Successful!</h4>
        <p><strong>Processing Time:</strong> {processing_time} seconds</p>
        <p><strong>Transcript Length:</strong> {len(transcript) if transcript else 'N/A'} characters</p>
        </div>
        """, unsafe_allow_html=True)

        # Download links
        if result_data.get("output_url"):
            st.markdown(f"🔗 **Download Result:** [Click here]({result_data['output_url']})")

        if transcript:
            with st.expander("📝 View Transcript"):
                st.text_area("Transcript", transcript, height=200)

    else:
        error_msg = result_data.get("error", "Unknown error occurred")
        st.markdown(f"""
        <div class="error-box">
        <h4>❌ Conversion Failed</h4>
        <p>{error_msg}</p>
        </div>
        """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
