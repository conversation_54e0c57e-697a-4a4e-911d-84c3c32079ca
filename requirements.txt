# Web Agents Requirements - Latest Available Versions
# Core web framework
fastapi==0.115.0
uvicorn==0.34.0
starlette==0.46.0

# AI and LLM packages
anthropic==0.52.0
openai==1.84.0
groq==0.26.0
langchain==0.3.25
langchain-anthropic==0.3.15
langchain-community==0.3.24
langchain-core==0.3.64
langchain-google-genai==2.1.5
langchain-groq==0.3.2
langchain-ollama==0.3.3
langchain-openai==0.3.19
langchain-text-splitters==0.3.8
langgraph==0.4.8
langgraph-checkpoint==2.0.26
langgraph-prebuilt==0.2.2
langgraph-sdk==0.1.70
langsmith==0.3.45

# Data processing
pandas==2.2.0
numpy==2.0.2
scikit-learn==1.5.0
scipy==1.14.0
openpyxl==3.1.5

# Database
psycopg2-binary==2.9.9
sqlalchemy==2.0.36

# Web and HTTP
requests==2.32.3
httpx==0.28.0
aiohttp==3.12.0

# UI and visualization
streamlit==1.40.0
altair==5.5.0
pillow==10.4.0

# Authentication and security
pyjwt==2.10.0
cryptography==43.0.0
pycryptodome==3.21.0

# Configuration and environment
python-dotenv==1.1.0
pydantic==2.11.0
pydantic-settings==2.9.0
python-multipart==0.0.19

# Voice_Over_Agent specific requirements
azure-cognitiveservices-speech==1.34.0
azure-storage-blob==12.18.0
azure-identity==1.15.0
azure-mgmt-storage==21.1.0
moviepy==1.0.3
pydub==0.25.1
colorlog==6.8.0

# Utilities
tqdm==4.67.0
click==8.1.8
pyyaml==6.0.2
toml==0.10.2

# Additional dependencies
jinja2==3.1.6
markupsafe==3.0.2
packaging==24.2
typing-extensions==4.12.0
certifi==2024.8.30
charset-normalizer==3.4.0
idna==3.10
urllib3==2.2.0
